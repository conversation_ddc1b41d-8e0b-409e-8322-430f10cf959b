/**
 * Apify Places Scraper
 * Gebruikt Apify Google Maps Scraper API in plaats van Google Places API
 * en verrijkt data met Firecrawl.
 */

require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');
const Airtable = require('airtable');
const ApifyPlacesClient = require('./services/apifyClient');
const firecrawlClient = require('./services/firecrawlClient');
const { addProfileToAirtable, mapApifyOnlyToAirtableFields, mapApifyWithLimitedFirecrawlToAirtableFields } = require('./services/airtableService');

// Airtable configuratie
const airtableToken = process.env.AIRTABLE_ACCESS_TOKEN;
const airtableBaseId = process.env.AIRTABLE_BASE_ID;
const airtableTableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

// Controleer configuratie
if (!airtableToken || !airtableBaseId) {
  throw new Error('AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten in .env staan');
}

// Initialiseer Airtable
const base = new Airtable({ apiKey: airtableToken }).base(airtableBaseId);

// Cache bestanden
const APIFY_CACHE_FILE = path.join(__dirname, '../data/processed_apify_places.json');
const APIFY_URL_CACHE_FILE = path.join(__dirname, '../data/processed_apify_urls.json');
const LOG_FILE = path.join(__dirname, '../logs/apify_scraper.log');

// Logger configuratie (hergebruik van bestaande logger)
class Logger {
  constructor(options = {}) {
    this.options = {
      logToConsole: true,
      logToFile: true,
      logLevel: 'info',
      ...options
    };
    
    this.logLevels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    
    this.logs = [];
  }
  
  shouldLog(level) {
    return this.logLevels[level] >= this.logLevels[this.options.logLevel];
  }
  
  formatTimestamp() {
    return new Date().toISOString();
  }
  
  async saveLogsToFile() {
    if (!this.options.logToFile || this.logs.length === 0) return;
    
    try {
      const logDir = path.dirname(LOG_FILE);
      try {
        await fs.mkdir(logDir, { recursive: true });
      } catch (err) {
        if (err.code !== 'EEXIST') throw err;
      }
      
      await fs.appendFile(LOG_FILE, this.logs.join('\n') + '\n');
      this.logs = [];
    } catch (error) {
      console.error('Kon logs niet opslaan naar bestand:', error);
    }
  }
  
  formatLog(level, message) {
    return `[${this.formatTimestamp()}] [${level.toUpperCase()}] ${message}`;
  }
  
  log(level, message) {
    if (!this.shouldLog(level)) return;
    
    const formattedMessage = this.formatLog(level, message);
    
    if (this.options.logToConsole) {
      const consoleMethod = level === 'error' ? 'error' : 
                           level === 'warn' ? 'warn' : 'log';
      console[consoleMethod](formattedMessage);
    }
    
    if (this.options.logToFile) {
      this.logs.push(formattedMessage);
    }
  }
  
  debug(message) { this.log('debug', message); }
  info(message) { this.log('info', message); }
  warn(message) { this.log('warn', message); }
  error(message) { this.log('error', message); }
}

/**
 * Apify Places Scraper klasse
 */
class ApifyScraper {
  constructor(options = {}) {
    this.options = {
      cacheProcessedPlaces: true,
      cacheProcessedUrls: true,
      delayBetweenRequests: 500, // GEOPTIMALISEERD: Sneller - van 1000ms naar 500ms
      maxPlacesPerSearch: 50,
      maxRetries: 3,
      logLevel: 'info',
      enableFirecrawlEnrichment: false, // Default uit voor snelle modus
      firecrawlMode: 'limited', // 'full', 'limited', 'disabled'
      language: 'nl',
      website: 'allPlaces', // 'allPlaces', 'onlyPlacesWithWebsite'
      searchMatching: 'all', // 'all', 'exact'
      skipClosedPlaces: false,
      batchSize: 5, // NIEUW: Parallelle verwerking in batches van 5
      ...options
    };
    
    this.processedPlaceIds = new Set();
    this.processedUrls = new Set();
    this.totalProcessed = 0;
    this.totalSaved = 0;
    this.totalErrors = 0;
    this.totalEnriched = 0;
    this.totalSkipped = 0;
    this.logger = new Logger({ logLevel: this.options.logLevel });
    
    this.startTime = null;
    this.apifyClient = null;
  }
  
  /**
   * Initialiseer de scraper
   */
  async initialize() {
    try {
      this.startTime = Date.now();
      this.logger.info('Initialiseren Apify scraper...');
      
      // Maak data directory als deze niet bestaat
      const dataDir = path.join(__dirname, '../data');
      try {
        await fs.mkdir(dataDir, { recursive: true });
      } catch (err) {
        if (err.code !== 'EEXIST') throw err;
      }
      
      // Initialiseer Apify client
      this.apifyClient = new ApifyPlacesClient();
      
      // Laad cache van verwerkte plaatsen indien aanwezig
      if (this.options.cacheProcessedPlaces) {
        try {
          const cacheData = await fs.readFile(APIFY_CACHE_FILE, 'utf8');
          const processedPlaces = JSON.parse(cacheData);
          this.processedPlaceIds = new Set(processedPlaces);
          this.logger.info(`Geladen cache met ${this.processedPlaceIds.size} verwerkte Apify plaatsen`);
        } catch (err) {
          if (err.code !== 'ENOENT') {
            this.logger.warn(`Waarschuwing bij laden cache: ${err.message}`);
          }
          this.logger.info('Geen bestaande cache voor Apify plaatsen gevonden, nieuwe cache aanmaken');
        }
      }
      
      // Laad cache van verwerkte URL's indien aanwezig
      if (this.options.cacheProcessedUrls) {
        try {
          const urlCacheData = await fs.readFile(APIFY_URL_CACHE_FILE, 'utf8');
          const processedUrls = JSON.parse(urlCacheData);
          this.processedUrls = new Set(processedUrls);
          this.logger.info(`Geladen cache met ${this.processedUrls.size} verwerkte Apify URL's`);
        } catch (err) {
          if (err.code !== 'ENOENT') {
            this.logger.warn(`Waarschuwing bij laden URL cache: ${err.message}`);
          }
          this.logger.info('Geen bestaande cache voor Apify URL\'s gevonden, nieuwe cache aanmaken');
        }
      }
      
      this.logger.info('Apify scraper geïnitialiseerd en klaar voor gebruik');
    } catch (error) {
      this.logger.error(`Fout bij initialiseren Apify scraper: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Sla de cache van verwerkte plaatsen op
   */
  async saveCache() {
    if (this.options.cacheProcessedPlaces) {
      try {
        await fs.writeFile(APIFY_CACHE_FILE, JSON.stringify([...this.processedPlaceIds], null, 2));
        this.logger.debug(`Cache opgeslagen: ${this.processedPlaceIds.size} verwerkte Apify plaatsen`);
      } catch (error) {
        this.logger.error(`Fout bij opslaan cache: ${error.message}`);
      }
    }
    
    if (this.options.cacheProcessedUrls) {
      try {
        await fs.writeFile(APIFY_URL_CACHE_FILE, JSON.stringify([...this.processedUrls], null, 2));
        this.logger.debug(`URL cache opgeslagen: ${this.processedUrls.size} verwerkte Apify URL's`);
      } catch (error) {
        this.logger.error(`Fout bij opslaan URL cache: ${error.message}`);
      }
    }
  }
  
  /**
   * Delay functie
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Hoofdfunctie voor het scrapen van plaatsen in een locatie
   * @param {Object} params - Parameters
   * @param {string} params.location - Locatie query (bijv. "Amsterdam, Netherlands")
   * @param {Array<string>} params.keywords - Array van zoektermen
   * @param {Object} params.options - Extra opties
   */
  async run(params) {
    const { location, keywords, options = {} } = params;
    
    try {
      await this.initialize();
      
      this.logger.info(`🚀 Start Apify scraping voor locatie: ${location}`);
      this.logger.info(`🔑 Zoektermen: ${keywords.join(', ')}`);
      this.logger.info(`📊 Max plaatsen per zoekterm: ${this.options.maxPlacesPerSearch}`);
      this.logger.info(`🔄 Firecrawl verrijking: ${this.options.enableFirecrawlEnrichment ? 'Ingeschakeld' : 'Uitgeschakeld'}`);
      
      // Zoek plaatsen via Apify
      const searchParams = {
        location,
        keywords,
        maxPlacesPerKeyword: this.options.maxPlacesPerSearch,
        options: {
          language: this.options.language,
          website: this.options.website,
          searchMatching: this.options.searchMatching,
          skipClosedPlaces: this.options.skipClosedPlaces
        }
      };
      
      const places = await this.apifyClient.searchPlacesInLocation(searchParams);
      this.logger.info(`📍 Gevonden ${places.length} plaatsen via Apify`);
      
      // GEOPTIMALISEERD: Verwerk plaatsen in parallelle batches voor snelheid
      const batchSize = this.options.batchSize;
      for (let i = 0; i < places.length; i += batchSize) {
        const batch = places.slice(i, i + batchSize);

        // Verwerk batch parallel
        const batchPromises = batch.map(async (place, batchIndex) => {
          try {
            await this.processPlace(place, i + batchIndex + 1, places.length);
            return { success: true, place: place.name };
          } catch (error) {
            this.logger.error(`Fout bij verwerken plaats ${place.name}: ${error.message}`);
            this.totalErrors++;
            return { success: false, place: place.name, error: error.message };
          }
        });

        // Wacht op voltooiing van batch
        const batchResults = await Promise.all(batchPromises);
        const successCount = batchResults.filter(r => r.success).length;

        this.logger.info(`Batch ${Math.floor(i/batchSize) + 1} voltooid: ${successCount}/${batch.length} succesvol`);

        // Korte pauze tussen batches (minder dan individuele delays)
        if (i + batchSize < places.length) {
          await this.delay(this.options.delayBetweenRequests);
        }

        // Sla cache op elke batch
        if ((i + batchSize) % (batchSize * 2) === 0) {
          await this.saveCache();
        }
      }
      
      // Finale cache save
      await this.saveCache();
      await this.logger.saveLogsToFile();
      
      this.logger.info('✅ Apify scraping voltooid!');
      this.printSummary();
      
    } catch (error) {
      this.logger.error(`Fatale fout tijdens Apify scraping: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verwerk een enkele plaats
   * @param {Object} place - Apify plaats object
   * @param {number} index - Index van de plaats
   * @param {number} total - Totaal aantal plaatsen
   */
  async processPlace(place, index, total) {
    const placeId = place.placeId || place.place_id || `${place.title || place.name}_${place.address}`;

    // Controleer of deze plaats al is verwerkt
    if (this.options.cacheProcessedPlaces && this.processedPlaceIds.has(placeId)) {
      this.logger.info(`[${index}/${total}] Plaats overgeslagen: ${place.title || place.name} (al verwerkt)`);
      this.totalSkipped++;
      return { skipped: true };
    }

    this.logger.info(`[${index}/${total}] Verwerken plaats: ${place.title || place.name}`);

    let enrichedData = null;
    let useProfileMapping = false;

    // Firecrawl verrijking indien ingeschakeld
    if (this.options.enableFirecrawlEnrichment && place.website) {
      try {
        this.logger.info(`Firecrawl verrijking voor: ${place.website}`);
        enrichedData = await firecrawlClient.enrichCompanyData(place.website);

        if (enrichedData) {
          this.logger.info('Firecrawl verrijking succesvol');
          this.totalEnriched++;
        } else {
          this.logger.warn('Geen verrijkte data ontvangen van Firecrawl');
        }
      } catch (error) {
        this.logger.warn(`Firecrawl verrijking mislukt: ${error.message}`);
      }
    }

    // Map data naar Airtable formaat
    let fields;
    if (this.options.enableFirecrawlEnrichment && enrichedData) {
      if (this.options.firecrawlMode === 'limited') {
        // Hybride modus: Apify + beperkte Firecrawl data
        fields = mapApifyWithLimitedFirecrawlToAirtableFields(place, enrichedData);
      } else {
        // Volledige modus: gebruik profile mapping
        const profile = {
          company: place.title || place.name || '',
          name: enrichedData.contactPerson?.name || '',
          website: place.website || '',
          linkedInUrl: '',
          enrichedData: {
            contact_person: enrichedData.contactPerson || {},
            company_contact: enrichedData.company_contact || {},
            summary: enrichedData.companySummary || ''
          }
        };
        fields = null;
        useProfileMapping = true;
      }
    } else {
      // Alleen Apify data
      fields = mapApifyOnlyToAirtableFields(place);
    }

    // Sla op naar Airtable
    try {
      let recordId;

      if (useProfileMapping) {
        recordId = await addProfileToAirtable(profile);
      } else {
        const records = await base(airtableTableName).create([{ fields }]);
        recordId = records[0].id;
      }

      if (recordId) {
        this.logger.info(`✅ Opgeslagen naar Airtable: ${recordId}`);
        this.totalSaved++;

        // Voeg toe aan verwerkte plaatsen
        if (this.options.cacheProcessedPlaces) {
          this.processedPlaceIds.add(placeId);
        }
      }
    } catch (error) {
      this.logger.error(`Fout bij opslaan naar Airtable: ${error.message}`);
      this.totalErrors++;
      throw error;
    }

    this.totalProcessed++;
    return { success: true };
  }

  /**
   * Print samenvatting van de scraping sessie
   */
  printSummary() {
    const endTime = Date.now();
    const duration = Math.round((endTime - this.startTime) / 1000 / 60); // minuten

    console.log('\n📊 Apify Scraping Samenvatting:');
    console.log(`⏱️  Totale duur: ${duration} minuten`);
    console.log(`📍 Totaal verwerkt: ${this.totalProcessed}`);
    console.log(`💾 Totaal opgeslagen: ${this.totalSaved}`);
    console.log(`🔄 Totaal verrijkt: ${this.totalEnriched}`);
    console.log(`⏭️  Totaal overgeslagen: ${this.totalSkipped}`);
    console.log(`❌ Totaal fouten: ${this.totalErrors}`);

    if (this.totalProcessed > 0) {
      const successRate = Math.round((this.totalSaved / this.totalProcessed) * 100);
      console.log(`✅ Succes percentage: ${successRate}%`);
    }

    console.log('\n🎯 Apify scraping voltooid!');
    console.log('📋 Check je Airtable voor alle verzamelde data');
  }
}

module.exports = ApifyScraper;
