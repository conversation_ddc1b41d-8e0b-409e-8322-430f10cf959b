require('dotenv').config();
const ApifyScraper = require('./apifyScraper');

async function testApify() {
  console.log('🚀 Test Apify scraper...\n');

  // Test locatie en zoektermen
  const location = 'Amsterdam, Netherlands';
  const keywords = ['restaurant', 'bakery']; // Kleine test set

  console.log('📋 Test configuratie:');
  console.log(`📍 Locatie: ${location}`);
  console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
  console.log(`🚀 Modus: Snelle Apify scraping (alleen Apify API)`);
  console.log(`📊 Max plaatsen per zoekterm: 5\n`);

  // Configureer scraper voor test - GEOPTIMALISEERD voor snelheid
  const scraper = new ApifyScraper({
    delayBetweenRequests: 300, // SNELLER: 300ms voor test (was 1000ms)
    maxPlacesPerSearch: 5, // Beperkt voor test
    enableFirecrawlEnrichment: false, // Snelle modus
    logLevel: 'info',
    language: 'nl',
    website: 'allPlaces',
    searchMatching: 'all',
    skipClosedPlaces: false,
    batchSize: 3 // Kleinere batches voor test
  });

  try {
    console.log('🔍 Starten van Apify test...\n');
    
    await scraper.run({
      location: location,
      keywords: keywords
    });

    console.log('\n✅ Apify test voltooid!');
    console.log('\n📊 Resultaten:');
    console.log(`- Totaal verwerkt: ${scraper.totalProcessed}`);
    console.log(`- Totaal opgeslagen: ${scraper.totalSaved}`);
    console.log(`- Totaal verrijkt: ${scraper.totalEnriched}`);
    console.log(`- Totaal fouten: ${scraper.totalErrors}`);
    
    console.log('\n💡 Apify test voordelen:');
    console.log('- Geen 60 resultaten limiet');
    console.log('- Meer gedetailleerde data');
    console.log('- Betere lokale coverage');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens Apify test:', error.message);
    console.error(error.stack);
  }
}

// Voer test uit
if (require.main === module) {
  testApify();
}

module.exports = testApify;
