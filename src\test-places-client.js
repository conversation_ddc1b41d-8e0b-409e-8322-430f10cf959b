/**
 * Test script voor de Places API client
 */

require('dotenv').config();
const placesApiClient = require('./services/placesApiClient');

// Test functie voor de Places API client
async function testPlacesApiClient() {
  try {
    console.log('Google Places API Client testen...\n');

    // Test 1: Tekst zoekopdracht
    console.log('Test 1: Tekst zoekopdracht uitvoeren...');
    const searchResults = await placesApiClient.textSearch('coffeeshops in Amsterdam', {
      language: 'nl'
    });
    
    console.log(`✅ Tekst zoekopdracht succesvol - ${searchResults.results.length} resultaten gevonden.`);
    console.log('Eerste resultaat:');
    const firstResult = searchResults.results[0];
    console.log(`- Naam: ${firstResult.name}`);
    console.log(`- Adres: ${firstResult.formatted_address}`);
    console.log(`- Rating: ${firstResult.rating}`);
    console.log(`- Place ID: ${firstResult.place_id}`);
    
    // Test 2: Details opvragen
    console.log('\nTest 2: Details opvragen...');
    const placeId = firstResult.place_id;
    // GEOPTIMALISEERD: alleen basis fields + website voor kostenreductie
    // Basis fields ($17/1000): name, formatted_address, geometry
    // Contact fields ($30/1000): website
    // VERWIJDERD voor kosten: rating, reviews, formatted_phone_number, opening_hours, photos
    const placeDetails = await placesApiClient.getPlaceDetails(placeId, {
      fields: 'name,formatted_address,geometry,website',
      language: 'nl'
    });
    
    console.log('✅ Details succesvol opgehaald:');
    console.log(`- Naam: ${placeDetails.name}`);
    console.log(`- Adres: ${placeDetails.formatted_address}`);
    console.log(`- Telefoon: ${placeDetails.formatted_phone_number || 'Niet beschikbaar'}`);
    console.log(`- Website: ${placeDetails.website || 'Niet beschikbaar'}`);
    console.log(`- Aantal reviews: ${placeDetails.reviews ? placeDetails.reviews.length : 0}`);
    
    // Test 3: Als er foto's zijn, toon de URL van de eerste foto
    if (placeDetails.photos && placeDetails.photos.length > 0) {
      console.log('\nTest 3: Foto URL genereren...');
      const photoReference = placeDetails.photos[0].photo_reference;
      const photoUrl = placesApiClient.getPhotoUrl(photoReference, 400);
      console.log('✅ Foto URL gegenereerd:');
      console.log(photoUrl);
    }
    
    // Test 4: Nearby search
    console.log('\nTest 4: Nearby search uitvoeren...');
    const location = firstResult.geometry.location;
    const nearbyResults = await placesApiClient.nearbySearch(
      location,
      500, // 500 meter radius
      'cafe', // type plaats
      { language: 'nl' }
    );
    
    console.log(`✅ Nearby search succesvol - ${nearbyResults.results.length} resultaten gevonden.`);
    console.log('Plaatsen in de buurt:');
    nearbyResults.results.slice(0, 3).forEach((place, index) => {
      console.log(`${index + 1}. ${place.name} - ${place.vicinity}`);
    });
    
    console.log('\nAlle tests geslaagd! De Places API client werkt correct.');
    
  } catch (error) {
    console.error('❌ Fout bij het testen van de Places API client:');
    console.error(error.message);
  }
}

// Voer de tests uit
testPlacesApiClient();