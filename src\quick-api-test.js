// Snelle test van de nieuwe API key
require('dotenv').config();

console.log('API Key Test:');
console.log('GOOGLE_PLACES_API_KEY:', process.env.GOOGLE_PLACES_API_KEY ? 'GEVONDEN' : 'NIET GEVONDEN');

if (process.env.GOOGLE_PLACES_API_KEY) {
  const key = process.env.GOOGLE_PLACES_API_KEY;
  console.log('Key lengte:', key.length);
  console.log('Key preview:', key.substring(0, 10) + '...' + key.substring(key.length - 4));
}

// Test axios import
try {
  const axios = require('axios');
  console.log('Axios:', 'GELADEN');
} catch (e) {
  console.log('Axios:', 'FOUT -', e.message);
}

console.log('Test voltooid.');
