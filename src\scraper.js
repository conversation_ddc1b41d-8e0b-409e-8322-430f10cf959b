/**
 * Google Places API Scraper
 * Gebruikt grid search strategie om meer dan 60 resultaten op te halen
 * en verrijkt data met Firecrawl.
 */

require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');
const Airtable = require('airtable');
const placesApiClient = require('./services/placesApiClient');
const firecrawlClient = require('./services/firecrawlClient'); // Importeer de nieuwe client
const { addProfileToAirtable, mapPlacesOnlyToAirtableFields, mapPlacesWithLimitedFirecrawlToAirtableFields } = require('./services/airtableService');

// Airtable configuratie
const airtableToken = process.env.AIRTABLE_ACCESS_TOKEN;
const airtableBaseId = process.env.AIRTABLE_BASE_ID;
const airtableTableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

// Controleer configuratie
if (!airtableToken || !airtableBaseId) {
  throw new Error('AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten in .env staan');
}

// Initialiseer Airtable
const base = new Airtable({ apiKey: airtableToken }).base(airtableBaseId);

// Cache bestand voor place_ids
const CACHE_FILE = path.join(__dirname, '../data/processed_places.json');
// Log bestand
const LOG_FILE = path.join(__dirname, '../logs/scraper.log');

// Cache bestand voor verwerkte URL's
const URL_CACHE_FILE = path.join(__dirname, '../data/processed_urls.json');

// Logger configuratie
class Logger {
  constructor(options = {}) {
    this.options = {
      logToConsole: true,
      logToFile: true,
      logLevel: 'info',     // 'debug', 'info', 'warn', 'error'
      ...options
    };
    
    this.logLevels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    
    this.logs = [];
  }
  
  shouldLog(level) {
    return this.logLevels[level] >= this.logLevels[this.options.logLevel];
  }
  
  formatTimestamp() {
    return new Date().toISOString();
  }
  
  async saveLogsToFile() {
    if (!this.options.logToFile || this.logs.length === 0) return;
    
    try {
      // Maak logs directory als deze niet bestaat
      const logDir = path.dirname(LOG_FILE);
      try {
        await fs.mkdir(logDir, { recursive: true });
      } catch (err) {
        if (err.code !== 'EEXIST') throw err;
      }
      
      // Voeg logs toe aan bestaand bestand of maak een nieuw bestand
      await fs.appendFile(LOG_FILE, this.logs.join('\n') + '\n');
      this.logs = []; // Reset logs na opslaan
    } catch (error) {
      console.error('Kon logs niet opslaan naar bestand:', error);
    }
  }
  
  formatLog(level, message) {
    return `[${this.formatTimestamp()}] [${level.toUpperCase()}] ${message}`;
  }
  
  log(level, message) {
    if (!this.shouldLog(level)) return;
    
    const formattedMessage = this.formatLog(level, message);
    
    if (this.options.logToConsole) {
      const consoleMethod = level === 'error' ? 'error' : 
                           level === 'warn' ? 'warn' : 'log';
      console[consoleMethod](formattedMessage);
    }
    
    if (this.options.logToFile) {
      this.logs.push(formattedMessage);
      
      // Als we 100 logs bereiken, sla ze op naar bestand
      if (this.logs.length >= 100) {
        this.saveLogsToFile();
      }
    }
  }
  
  debug(message) { this.log('debug', message); }
  info(message) { this.log('info', message); }
  warn(message) { this.log('warn', message); }
  error(message) { this.log('error', message); }
}

// Scraper configuratie
class PlacesScraper {
  constructor(options = {}) {
    this.options = {
      cacheProcessedPlaces: true,
      cacheProcessedUrls: true, // Nieuwe optie om URL caching aan/uit te zetten
      delayBetweenRequests: 200,
      delayForPageToken: 2000,
      maxPlacesPerGrid: 60,
      maxRetries: 3,
      logLevel: 'info',
      enableFirecrawlEnrichment: true,
      firecrawlMode: 'full', // 'full', 'limited', 'disabled'
      ...options
    };
    
    this.processedPlaceIds = new Set();
    this.processedUrls = new Set(); // Nieuwe set voor verwerkte URL's
    this.totalProcessed = 0;
    this.totalSaved = 0;
    this.totalErrors = 0;
    this.totalEnriched = 0;
    this.totalSkipped = 0; // Nieuwe teller voor overgeslagen URL's
    this.logger = new Logger({ logLevel: this.options.logLevel });
    
    this.startTime = null;
  }
  
  /**
   * Initialiseer de scraper
   */
  async initialize() {
    try {
      this.startTime = Date.now();
      this.logger.info('Initialiseren scraper...');
      
      // Maak data directory als deze niet bestaat
      const dataDir = path.join(__dirname, '../data');
      try {
        await fs.mkdir(dataDir, { recursive: true });
      } catch (err) {
        if (err.code !== 'EEXIST') throw err;
      }
      
      // Laad cache van verwerkte plaatsen indien aanwezig
      if (this.options.cacheProcessedPlaces) {
        try {
          const cacheData = await fs.readFile(CACHE_FILE, 'utf8');
          const processedPlaces = JSON.parse(cacheData);
          this.processedPlaceIds = new Set(processedPlaces);
          this.logger.info(`Geladen cache met ${this.processedPlaceIds.size} verwerkte plaatsen`);
        } catch (err) {
          if (err.code !== 'ENOENT') {
            this.logger.warn(`Waarschuwing bij laden cache: ${err.message}`);
          }
          this.logger.info('Geen bestaande cache voor plaatsen gevonden, nieuwe cache aanmaken');
        }
      }
      
      // Laad cache van verwerkte URL's indien aanwezig
      if (this.options.cacheProcessedUrls) {
        try {
          const urlCacheData = await fs.readFile(URL_CACHE_FILE, 'utf8');
          const processedUrls = JSON.parse(urlCacheData);
          this.processedUrls = new Set(processedUrls);
          this.logger.info(`Geladen cache met ${this.processedUrls.size} verwerkte URL's`);
        } catch (err) {
          if (err.code !== 'ENOENT') {
            this.logger.warn(`Waarschuwing bij laden URL cache: ${err.message}`);
          }
          this.logger.info('Geen bestaande cache voor URL\'s gevonden, nieuwe cache aanmaken');
        }
      }
      
      this.logger.info('Scraper geïnitialiseerd en klaar voor gebruik');
    } catch (error) {
      this.logger.error(`Fout bij initialiseren scraper: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Sla de cache van verwerkte plaatsen op
   */
  async saveCache() {
    if (this.options.cacheProcessedPlaces) {
      try {
        await fs.writeFile(CACHE_FILE, JSON.stringify([...this.processedPlaceIds]), 'utf8');
        this.logger.info(`Cache opgeslagen met ${this.processedPlaceIds.size} verwerkte plaatsen`);
      } catch (error) {
        this.logger.error(`Fout bij opslaan cache: ${error.message}`);
      }
    }
    
    // Sla de cache van verwerkte URL's op
    if (this.options.cacheProcessedUrls) {
      try {
        await fs.writeFile(URL_CACHE_FILE, JSON.stringify([...this.processedUrls]), 'utf8');
        this.logger.info(`URL cache opgeslagen met ${this.processedUrls.size} verwerkte URL's`);
      } catch (error) {
        this.logger.error(`Fout bij opslaan URL cache: ${error.message}`);
      }
    }
  }
  
  /**
   * Normaliseer een URL voor consistente opslag in de cache
   * @param {string} url - De URL om te normaliseren
   * @returns {string} - De genormaliseerde URL
   */
  normalizeUrl(url) {
    try {
      // Verwijder trailing slash, protocol en www.
      let normalized = url.trim().toLowerCase();
      
      // Verwijder protocol als aanwezig
      if (normalized.startsWith('http://')) normalized = normalized.substring(7);
      if (normalized.startsWith('https://')) normalized = normalized.substring(8);
      
      // Verwijder www. als aanwezig
      if (normalized.startsWith('www.')) normalized = normalized.substring(4);
      
      // Verwijder trailing slash
      if (normalized.endsWith('/')) normalized = normalized.substring(0, normalized.length - 1);
      
      return normalized;
    } catch (error) {
      // Bij fouten, retourneer de originele URL
      return url;
    }
  }
  
  /**
   * Verwerkt een URL met zowel Firecrawl als Google Places API
   * @param {string} url - De URL om te verwerken
   * @returns {Promise<Object|null>} - De verrijkte data of null bij een fout
   */
  async processUrlWithPlacesApi(url) {
    try {
      this.logger.info(`Verwerken van URL: ${url}`);
      
      // Normaliseer de URL voor cache controle
      const normalizedUrl = this.normalizeUrl(url);
      
      // Controleer of de URL al is verwerkt
      if (this.processedUrls.has(normalizedUrl)) {
        this.logger.info(`URL overgeslagen: ${url} (al verwerkt)`);
        this.totalSkipped++;
        return { skipped: true, url };
      }
      
      // Stap 1: Extraheer de domeinnaam voor basis informatie
      const domainName = this.extractDomainName(url);
      
      // Stap 2: Verrijk eerst met Firecrawl om bedrijfsnaam te krijgen
      let enrichedData = null;
      let companyName = domainName; // Fallback naar domeinnaam
      
      if (this.options.enableFirecrawlEnrichment) {
        try {
          this.logger.info(`Firecrawl verrijking starten voor: ${url}`);
          enrichedData = await firecrawlClient.enrichCompanyData(url);
          
          if (enrichedData) {
            this.logger.info(`Firecrawl verrijking succesvol voor: ${url}`);
            this.totalEnriched++;
            
            // Gebruik de bedrijfsnaam uit Firecrawl indien beschikbaar
            if (enrichedData.companyName) {
              companyName = enrichedData.companyName;
              this.logger.info(`Bedrijfsnaam uit Firecrawl: ${companyName}`);
            }
          } else {
            this.logger.warn(`Geen Firecrawl data ontvangen voor: ${url}`);
          }
        } catch (error) {
          this.logger.warn(`Fout bij Firecrawl verrijking: ${error.message}`);
        }
      }
      
      // Stap 3: Zoek het bedrijf via de Places API met de gevonden bedrijfsnaam
      let placeDetails = null;
      let placeId = null;
      
      try {
        this.logger.info(`Places API zoeken naar: ${companyName}`);
        const searchResults = await placesApiClient.searchPlaces({
          query: companyName,
          type: 'establishment',
          language: 'nl'
        });
        
        if (searchResults && searchResults.results && searchResults.results.length > 0) {
          // Neem het eerste resultaat
          placeId = searchResults.results[0].place_id;
          this.logger.info(`Gevonden place_id: ${placeId}`);
          
          // Haal details op - GEOPTIMALISEERD: alleen basis fields + website
          // Basis fields ($17/1000): place_id, name, formatted_address, geometry, types
          // Contact fields ($30/1000): website
          // VERWIJDERD: rating, user_ratings_total (Atmosphere $50/1000), formatted_phone_number (Contact $30/1000), url
          placeDetails = await placesApiClient.getPlaceDetails(placeId, {
            fields: 'place_id,name,formatted_address,geometry,website,types',
            language: 'nl'
          });
          
          this.logger.info(`Places API details opgehaald voor: ${placeDetails.name}`);
          
          // Voeg place_id toe aan verwerkte plaatsen
          if (placeId && this.options.cacheProcessedPlaces) {
            this.processedPlaceIds.add(placeId);
          }
        } else {
          this.logger.warn(`Geen Places API resultaten gevonden voor: ${companyName}`);
        }
      } catch (error) {
        this.logger.warn(`Fout bij Places API zoekopdracht: ${error.message}`);
      }
      
      // Stap 4: Combineer de gegevens en sla op in Airtable
      const fields = {
        'Name': placeDetails ? placeDetails.name : (enrichedData && enrichedData.companyName ? enrichedData.companyName : domainName),
        'Website': url
      };
      
      // Voeg Places API gegevens toe
      if (placeDetails) {
        // Voeg Place ID toe
        if (placeDetails.place_id) fields['Place ID'] = placeDetails.place_id;
        
        if (placeDetails.formatted_address) fields['Address'] = placeDetails.formatted_address;
        // VERWIJDERD: formatted_phone_number (Contact field $30/1000) - niet meer opgevraagd
        // if (placeDetails.formatted_phone_number) fields['Phone'] = placeDetails.formatted_phone_number;

        // VERWIJDERD: Rating en Total Ratings (Atmosphere fields $50/1000) - niet meer opgevraagd
        // if (placeDetails.rating !== undefined) fields['Rating'] = placeDetails.rating;
        // if (placeDetails.user_ratings_total !== undefined) fields['Total Ratings'] = placeDetails.user_ratings_total;

        // VERWIJDERD: url field - niet meer opgevraagd
        // if (placeDetails.url) fields['Search Page URL'] = placeDetails.url;
        
        // Voeg coördinaten toe
        if (placeDetails.geometry && placeDetails.geometry.location) {
          fields['Latitude'] = placeDetails.geometry.location.lat;
          fields['Longitude'] = placeDetails.geometry.location.lng;
        }
        
        // Voeg types toe
        if (placeDetails.types && placeDetails.types.length > 0) {
          fields['Types'] = placeDetails.types.join(', ');
        }
      }
      
      // Voeg Firecrawl verrijkte data toe
      if (enrichedData) {
        if (enrichedData.contactPerson) {
          if (enrichedData.contactPerson.name) fields['Contact Person'] = enrichedData.contactPerson.name;
          if (enrichedData.contactPerson.title) fields['Contact Title'] = enrichedData.contactPerson.title;
          if (enrichedData.contactPerson.department) fields['Contact Department'] = enrichedData.contactPerson.department;
          if (enrichedData.contactPerson.email) fields['Contact Email'] = enrichedData.contactPerson.email;
          if (enrichedData.contactPerson.phone) fields['Contact Phone'] = enrichedData.contactPerson.phone;
        }
        
        if (enrichedData.companySummary) {
          fields['Company Summary'] = enrichedData.companySummary.substring(0, 1000);
        }
        
        if (enrichedData.services) {
          fields['Has VMware'] = enrichedData.services.hasVMware ?? false;
          fields['Has Cloud Computing'] = enrichedData.services.hasCloudComputing ?? false;
          fields['Has Cloud Solutions'] = enrichedData.services.hasCloudSolutions ?? false;
          fields['Has Virtualization'] = enrichedData.services.hasVirtualization ?? false;
        }
      }
      
      // Opslaan in Airtable (hybride aanpak)
      let retries = 0;
      const maxRetries = this.options.maxRetries;
      let saveSuccess = false;
      while (retries < maxRetries && !saveSuccess) {
        try {
          if (enrichedData) {
            // Mapping naar profiel-structuur voor enrichedData
            const profile = {
              company: fields['Name'] || '',
              name: enrichedData.contactPerson?.name || '',
              website: fields['Website'] || '',
              linkedInUrl: '', // Niet beschikbaar in deze flow
              enrichedData: {
                contact_person: enrichedData.contactPerson || {},
                company_contact: enrichedData.company_contact || {},
                summary: enrichedData.companySummary || ''
              }
            };
            await addProfileToAirtable(profile);
          } else {
          await base(airtableTableName).create([{ fields }], { typecast: true });
          }
          this.totalSaved++;
          this.logger.info(`Opgeslagen in Airtable: ${fields['Name'] || url}`);
          saveSuccess = true;
        } catch (err) {
          retries++;
          this.logger.warn(`Airtable fout bij opslaan "${fields['Name'] || url}" (poging ${retries}/${maxRetries}): ${err.message}`);
          if (retries >= maxRetries) {
            this.logger.error(`Definitieve Airtable fout voor "${fields['Name'] || url}": ${err.message}`);
            this.totalErrors++;
            // Retourneer de gecombineerde data ondanks de Airtable fout
            return { placeDetails, enrichedData };
          }
          // Exponentiële backoff
          const backoffDelay = this.options.delayBetweenRequests * Math.pow(2, retries);
          await this.delay(backoffDelay);
        }
      }
      
      // Voeg URL toe aan verwerkte URL's
      if (this.options.cacheProcessedUrls) {
        this.processedUrls.add(normalizedUrl);
        
        // Sla de cache periodiek op (bijvoorbeeld elke 10 URL's)
        if (this.totalSaved % 10 === 0) {
          await this.saveCache();
        }
      }
      
      // Retourneer de gecombineerde data
      return { placeDetails, enrichedData };
    } catch (error) {
      this.logger.error(`Fout bij verwerken van URL ${url}: ${error.message}`);
      this.totalErrors++;
      return null;
    }
  }
  
  /**
   * Log eindstatistieken inclusief overgeslagen URL's
   */
  logFinalStats() {
    const endTime = Date.now();
    const durationMs = endTime - this.startTime;
    const durationMin = Math.floor(durationMs / 60000);
    const durationSec = Math.floor((durationMs % 60000) / 1000);
    
    this.logger.info('=== Eindresultaten ===');
    this.logger.info(`Totaal verwerkt: ${this.totalProcessed}`);
    this.logger.info(`Totaal opgeslagen: ${this.totalSaved}`);
    this.logger.info(`Totaal verrijkt: ${this.totalEnriched}`);
    this.logger.info(`Totaal overgeslagen: ${this.totalSkipped}`);
    this.logger.info(`Totaal fouten: ${this.totalErrors}`);
    this.logger.info(`Totale duur: ${durationMin}m ${durationSec}s`);
    
    // Sla de cache op aan het einde
    this.saveCache();
  }
  
  /**
   * Hulpmethode voor vertraging
   * @param {number} ms - Aantal milliseconden
   * @returns {Promise} - Een promise die resolvet na de vertraging
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Extraheert de domeinnaam uit een URL
   * @param {string} url - De URL
   * @returns {string} - De domeinnaam
   */
  extractDomainName(url) {
    try {
      const urlObj = new URL(url);
      let domain = urlObj.hostname;
      
      // Verwijder www. prefix als die aanwezig is
      if (domain.startsWith('www.')) {
        domain = domain.substring(4);
      }
      
      // Maak eerste letter van elk deel hoofdletter voor een mooiere weergave
      return domain.split('.').map(part => 
        part.charAt(0).toUpperCase() + part.slice(1)
      ).join('.');
    } catch (error) {
      // Als de URL niet geldig is, retourneer de originele URL
      return url;
    }
  }

  /**
   * Voert een grid-gebaseerde zoekopdracht uit
   * @param {Object} params - Parameters voor de zoekopdracht
   * @param {Object} params.center - Middelpunt {lat, lng}
   * @param {number} params.radiusKm - Radius in kilometers
   * @param {string[]} params.keywords - Zoektermen
   * @param {number} params.gridSizeM - Grid cel grootte in meters
   * @returns {Promise<void>}
   */
  async run(params) {
    try {
      // Initialiseer de scraper
      await this.initialize();
      
      const { center, radiusKm, keywords, gridSizeM = 500 } = params;
      
      this.logger.info(`Start grid-gebaseerde zoekopdracht rond ${center.lat}, ${center.lng}`);
      this.logger.info(`Radius: ${radiusKm}km, Grid grootte: ${gridSizeM}m, Zoektermen: ${keywords.join(', ')}`);
      
      // Bereken grid coördinaten
      const gridPoints = this.calculateGridPoints(center, radiusKm, gridSizeM);
      this.logger.info(`Grid berekend met ${gridPoints.length} punten`);
      
      // Doorloop alle zoektermen
      for (const keyword of keywords) {
        this.logger.info(`Start zoeken voor term: "${keyword}"`);
        
        // Doorloop alle grid punten
        for (let i = 0; i < gridPoints.length; i++) {
          const point = gridPoints[i];
          this.logger.info(`Verwerken grid punt ${i+1}/${gridPoints.length}: ${point.lat}, ${point.lng}`);
          
          // Zoek plaatsen rond dit punt
          await this.searchPlacesAroundPoint(point, keyword);
          
          // Toon voortgang
          const progress = ((i + 1) / gridPoints.length * 100).toFixed(1);
          this.logger.info(`Voortgang voor "${keyword}": ${progress}% (${i+1}/${gridPoints.length} punten)`);
        }
      }
      
      // Sla de cache op
      await this.saveCache();
      
      // Log eindresultaten
      this.logFinalStats();
      
    } catch (error) {
      this.logger.error(`Fout bij uitvoeren grid-zoekopdracht: ${error.message}`);
      throw error;
    }
  }

  /**
   * Berekent grid punten binnen een cirkel
   * @param {Object} center - Middelpunt {lat, lng}
   * @param {number} radiusKm - Radius in kilometers
   * @param {number} gridSizeM - Grid cel grootte in meters
   * @returns {Array<{lat: number, lng: number}>} - Array van grid punten
   */
  calculateGridPoints(center, radiusKm, gridSizeM) {
    // Converteer radius van km naar meters
    const radiusM = radiusKm * 1000;
    
    // Bereken hoeveel grid cellen we nodig hebben in elke richting
    const gridCount = Math.ceil(radiusM / gridSizeM);
    
    // Bereken de afstand in graden voor de grid grootte
    // 111,111 meter is ongeveer 1 graad latitude
    const latDelta = gridSizeM / 111111;
    
    // Longitude afstand varieert met de latitude
    const lngDelta = gridSizeM / (111111 * Math.cos(center.lat * Math.PI / 180));
    
    const gridPoints = [];
    
    // Genereer grid punten
    for (let i = -gridCount; i <= gridCount; i++) {
      for (let j = -gridCount; j <= gridCount; j++) {
        const lat = center.lat + i * latDelta;
        const lng = center.lng + j * lngDelta;
        
        // Bereken afstand tot middelpunt
        const distance = this.calculateDistance(center, { lat, lng });
        
        // Voeg alleen punten toe binnen de radius
        if (distance <= radiusKm) {
          gridPoints.push({ lat, lng });
        }
      }
    }
    
    return gridPoints;
  }

  /**
   * Berekent de afstand tussen twee punten in kilometers (Haversine formule)
   * @param {Object} point1 - Eerste punt {lat, lng}
   * @param {Object} point2 - Tweede punt {lat, lng}
   * @returns {number} - Afstand in kilometers
   */
  calculateDistance(point1, point2) {
    const R = 6371; // Aarde radius in km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLng = (point2.lng - point1.lng) * Math.PI / 180;
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Zoekt naar plaatsen rond een punt
   * @param {Object} point - Punt {lat, lng}
   * @param {string} keyword - Zoekterm
   */
  async searchPlacesAroundPoint(point, keyword) {
    try {
      let pageToken = null;
      let totalFound = 0;
      
      do {
        // Wacht even als we een pageToken hebben (Google API vereiste)
        if (pageToken) {
          await this.delay(this.options.delayForPageToken);
        }
        
        // Zoek plaatsen
        const params = {
          location: point,
          radius: 1000, // 1km radius rond elk punt
          keyword: keyword,
          language: 'nl',
          pagetoken: pageToken
        };
        
        const response = await placesApiClient.nearbySearch(params);
        
        if (!response || !response.results) {
          this.logger.warn(`Geen resultaten ontvangen van Places API voor ${point.lat}, ${point.lng}`);
          break;
        }
        
        // Verwerk resultaten
        for (const place of response.results) {
          if (this.processedPlaceIds.has(place.place_id)) {
            this.logger.debug(`Plaats overgeslagen: ${place.name} (al verwerkt)`);
            continue;
          }
          
          this.totalProcessed++;
          
          // Haal details op
          try {
            await this.delay(this.options.delayBetweenRequests);
            
            // GEOPTIMALISEERD: alleen basis fields + website voor kostenreductie
            // Basis fields ($17/1000): place_id, name, formatted_address, geometry, types
            // Contact fields ($30/1000): website
            // VERWIJDERD: rating, user_ratings_total (Atmosphere $50/1000), formatted_phone_number (Contact $30/1000), url
            const details = await placesApiClient.getPlaceDetails(place.place_id, {
              fields: 'place_id,name,formatted_address,geometry,website,types',
              language: 'nl'
            });
            
            // Verrijk met Firecrawl als er een website is
            let enrichedData = null;
            if (this.options.enableFirecrawlEnrichment && details.website) {
              try {
                this.logger.info(`Verrijken van data voor ${details.name} (${details.website})`);
                enrichedData = await firecrawlClient.enrichCompanyData(details.website);
                
                if (enrichedData) {
                  this.logger.info(`Verrijking succesvol voor: ${details.name}`);
                  this.totalEnriched++;
                } else {
                  this.logger.warn(`Geen verrijkte data ontvangen voor ${details.name}`);
                }
              } catch (error) {
                this.logger.warn(`Fout bij verrijken van ${details.name}: ${error.message}`);
              }
            }
            
            // Bereid Airtable velden voor - gebruik nieuwe mapping
            let fields;
            let useProfileMapping = false;

            if (this.options.enableFirecrawlEnrichment && enrichedData) {
              if (this.options.firecrawlMode === 'limited') {
                // Hybride modus: Places API + beperkte Firecrawl data
                fields = mapPlacesWithLimitedFirecrawlToAirtableFields(details, enrichedData);
              } else {
                // Volledige modus: gebruik profile mapping
                const profile = {
                  company: details.name || '',
                  name: enrichedData.contactPerson?.name || '',
                  website: details.website || '',
                  linkedInUrl: '', // Niet beschikbaar in deze flow
                  enrichedData: {
                    contact_person: enrichedData.contactPerson || {},
                    company_contact: enrichedData.company_contact || {},
                    summary: enrichedData.companySummary || ''
                  }
                };
                fields = null; // Signaal dat we de profile mapping gebruiken
                useProfileMapping = true;
              }
            } else {
              // Gebruik alleen Places API data (snelle modus)
              fields = mapPlacesOnlyToAirtableFields(details);
            }
            
            // Opslaan in Airtable
            let retries = 0;
            const maxRetries = this.options.maxRetries;
            let saveSuccess = false;
            while (retries < maxRetries && !saveSuccess) {
              try {
                if (useProfileMapping && fields === null) {
                  // Gebruik profiel mapping voor volledige Firecrawl data
                  const profile = {
                    company: details.name || '',
                    name: enrichedData.contactPerson?.name || '',
                    website: details.website || '',
                    linkedInUrl: '', // Niet beschikbaar in deze flow
                    enrichedData: {
                      contact_person: enrichedData.contactPerson || {},
                      company_contact: enrichedData.company_contact || {},
                      summary: enrichedData.companySummary || ''
                    }
                  };
                  await addProfileToAirtable(profile);
                } else {
                  // Gebruik directe Airtable opslag voor Places-only of hybride data
                  await base(airtableTableName).create([{ fields }], { typecast: true });
                }
                this.totalSaved++;
                this.logger.info(`Opgeslagen in Airtable: ${details.name}`);
                saveSuccess = true;
              } catch (err) {
                retries++;
                this.logger.warn(`Airtable fout bij opslaan "${details.name}" (poging ${retries}/${maxRetries}): ${err.message}`);
                if (retries >= maxRetries) {
                  this.logger.error(`Definitieve Airtable fout voor "${details.name}": ${err.message}`);
                  this.totalErrors++;
                  // Retourneer de gecombineerde data ondanks de Airtable fout
                  return { placeDetails: details, enrichedData };
                }
                // Exponentiële backoff
                const backoffDelay = this.options.delayBetweenRequests * Math.pow(2, retries);
                await this.delay(backoffDelay);
              }
            }
            
            // Voeg toe aan verwerkte plaatsen
            this.processedPlaceIds.add(place.place_id);
            
            // Sla de cache periodiek op
            if (this.totalSaved % 10 === 0) {
              await this.saveCache();
            }
            
            totalFound++;
            
          } catch (error) {
            this.logger.error(`Fout bij verwerken van plaats ${place.name}: ${error.message}`);
            this.totalErrors++;
          }
        }
        
        // Update pageToken voor volgende pagina
        pageToken = response.next_page_token;
        
        // Controleer of we het maximum aantal plaatsen per grid hebben bereikt
        if (totalFound >= this.options.maxPlacesPerGrid) {
          this.logger.info(`Maximum aantal plaatsen bereikt voor dit grid punt (${this.options.maxPlacesPerGrid})`);
          break;
        }
        
      } while (pageToken);
      
      this.logger.info(`Gevonden plaatsen rond ${point.lat}, ${point.lng} voor "${keyword}": ${totalFound}`);
      
    } catch (error) {
      this.logger.error(`Fout bij zoeken rond punt ${point.lat}, ${point.lng}: ${error.message}`);
    }
  }

  /**
   * Verwerkt een bedrijfsnaam met Google Places API en Firecrawl
   * @param {string} companyName - De bedrijfsnaam om te verwerken
   * @param {string} country - Het land voor de zoekopdracht
   * @returns {Promise<Object|null>} - De verrijkte data of null bij een fout
   */
  async processCompanyName(companyName, country) {
    try {
      this.logger.info(`Verwerken van bedrijfsnaam: ${companyName} in ${country}`);
      
      // Normaliseer de bedrijfsnaam voor de cache
      const normalizedName = `${companyName.toLowerCase().trim()}_${country.toLowerCase().trim()}`;
      
      // Controleer of deze naam al is verwerkt
      if (this.options.cacheProcessedUrls && this.processedUrls.has(normalizedName)) {
        this.logger.info(`Bedrijfsnaam overgeslagen: ${companyName} (al verwerkt)`);
        this.totalSkipped++;
        return { skipped: true };
      }
      
      // Stap 1: Zoek het bedrijf via de Places API
      let placeDetails = null;
      let placeId = null;
      
      try {
        this.logger.info(`Places API zoeken naar: ${companyName} in ${country}`);
        const searchResults = await placesApiClient.searchPlaces({
          query: `${companyName} ${country}`,
          type: 'establishment',
          language: 'nl'
        });
        
        if (searchResults && searchResults.results && searchResults.results.length > 0) {
          // Neem het eerste resultaat
          placeId = searchResults.results[0].place_id;
          this.logger.info(`Gevonden place_id: ${placeId}`);
          
          // Haal details op - GEOPTIMALISEERD: alleen basis fields + website
          // Basis fields ($17/1000): place_id, name, formatted_address, geometry, types
          // Contact fields ($30/1000): website
          // VERWIJDERD: rating, user_ratings_total (Atmosphere $50/1000), formatted_phone_number (Contact $30/1000), url
          placeDetails = await placesApiClient.getPlaceDetails(placeId, {
            fields: 'place_id,name,formatted_address,geometry,website,types',
            language: 'nl'
          });
          
          this.logger.info(`Places API details opgehaald voor: ${placeDetails.name}`);
        } else {
          this.logger.warn(`Geen Places API resultaten gevonden voor: ${companyName} in ${country}`);
        }
      } catch (error) {
        this.logger.warn(`Fout bij Places API zoekopdracht: ${error.message}`);
      }
      
      // Stap 2: Verrijk met Firecrawl als er een website is
      let enrichedData = null;
      
      if (this.options.enableFirecrawlEnrichment && placeDetails && placeDetails.website) {
        try {
          this.logger.info(`Firecrawl verrijking starten voor: ${placeDetails.website}`);
          enrichedData = await firecrawlClient.enrichCompanyData(placeDetails.website);
          
          if (enrichedData) {
            this.logger.info(`Firecrawl verrijking succesvol voor: ${placeDetails.website}`);
            this.totalEnriched++;
          } else {
            this.logger.warn(`Geen Firecrawl data ontvangen voor ${placeDetails.website}`);
          }
        } catch (error) {
          this.logger.warn(`Fout bij Firecrawl verrijking: ${error.message}`);
        }
      }
      
      // Als we geen gegevens hebben gevonden, return null
      if (!placeDetails) {
        this.logger.warn(`Geen gegevens gevonden voor: ${companyName}`);
        return null;
      }
      
      // Stap 3: Combineer de gegevens en sla op in Airtable
      const fields = {
        'Name': placeDetails.name
        // Geen extra velden voor de zoekopdracht
      };
      
      // Voeg Places API gegevens toe
      if (placeDetails) {
        // Voeg Place ID toe
        if (placeDetails.place_id) fields['Place ID'] = placeDetails.place_id;
        
        if (placeDetails.formatted_address) fields['Address'] = placeDetails.formatted_address;
        // VERWIJDERD: formatted_phone_number (Contact field $30/1000) - niet meer opgevraagd
        // if (placeDetails.formatted_phone_number) fields['Phone'] = placeDetails.formatted_phone_number;
        if (placeDetails.website) fields['Website'] = placeDetails.website;

        // VERWIJDERD: Rating en Total Ratings (Atmosphere fields $50/1000) - niet meer opgevraagd
        // if (placeDetails.rating !== undefined) fields['Rating'] = placeDetails.rating;
        // if (placeDetails.user_ratings_total !== undefined) fields['Total Ratings'] = placeDetails.user_ratings_total;

        // VERWIJDERD: url field - niet meer opgevraagd
        // if (placeDetails.url) fields['Search Page URL'] = placeDetails.url;
        
        // Voeg coördinaten toe
        if (placeDetails.geometry && placeDetails.geometry.location) {
          fields['Latitude'] = placeDetails.geometry.location.lat;
          fields['Longitude'] = placeDetails.geometry.location.lng;
        }
        
        // Voeg types toe
        if (placeDetails.types && placeDetails.types.length > 0) {
          fields['Types'] = placeDetails.types.join(', ');
        }
      }
      
      // Voeg Firecrawl verrijkte data toe
      if (enrichedData) {
        if (enrichedData.contactPerson) {
          if (enrichedData.contactPerson.name) fields['Contact Person'] = enrichedData.contactPerson.name;
          if (enrichedData.contactPerson.title) fields['Contact Title'] = enrichedData.contactPerson.title;
          if (enrichedData.contactPerson.department) fields['Contact Department'] = enrichedData.contactPerson.department;
          if (enrichedData.contactPerson.email) fields['Contact Email'] = enrichedData.contactPerson.email;
          if (enrichedData.contactPerson.phone) fields['Contact Phone'] = enrichedData.contactPerson.phone;
        }
        
        if (enrichedData.companySummary) {
          fields['Company Summary'] = enrichedData.companySummary.substring(0, 1000);
        }
        
        if (enrichedData.services) {
          fields['Has VMware'] = enrichedData.services.hasVMware ?? false;
          fields['Has Cloud Computing'] = enrichedData.services.hasCloudComputing ?? false;
          fields['Has Cloud Solutions'] = enrichedData.services.hasCloudSolutions ?? false;
          fields['Has Virtualization'] = enrichedData.services.hasVirtualization ?? false;
        }
      }
      
      // Opslaan in Airtable (hybride aanpak)
      let retries = 0;
      const maxRetries = this.options.maxRetries;
      let saveSuccess = false;
      while (retries < maxRetries && !saveSuccess) {
        try {
          if (enrichedData) {
            // Mapping naar profiel-structuur voor enrichedData
            const profile = {
              company: fields['Name'] || '',
              name: enrichedData.contactPerson?.name || '',
              website: fields['Website'] || '',
              linkedInUrl: '', // Niet beschikbaar in deze flow
              enrichedData: {
                contact_person: enrichedData.contactPerson || {},
                company_contact: enrichedData.company_contact || {},
                summary: enrichedData.companySummary || ''
              }
            };
            await addProfileToAirtable(profile);
          } else {
          await base(airtableTableName).create([{ fields }], { typecast: true });
          }
          this.totalSaved++;
          this.logger.info(`Opgeslagen in Airtable: ${fields['Name'] || companyName}`);
          saveSuccess = true;
        } catch (err) {
          retries++;
          this.logger.warn(`Airtable fout bij opslaan "${fields['Name'] || companyName}" (poging ${retries}/${maxRetries}): ${err.message}`);
          if (retries >= maxRetries) {
            this.logger.error(`Definitieve Airtable fout voor "${fields['Name'] || companyName}": ${err.message}`);
            this.totalErrors++;
            // Retourneer de gecombineerde data ondanks de Airtable fout
            return { placeDetails, enrichedData };
          }
          // Exponentiële backoff
          const backoffDelay = this.options.delayBetweenRequests * Math.pow(2, retries);
          await this.delay(backoffDelay);
        }
      }
      
      // Voeg toe aan verwerkte namen
      if (this.options.cacheProcessedUrls) {
        this.processedUrls.add(normalizedName);
        
        // Sla de cache periodiek op (bijvoorbeeld elke 10 namen)
        if (this.totalSaved % 10 === 0) {
          await this.saveCache();
        }
      }
      
      // Retourneer de gecombineerde data
      return { placeDetails, enrichedData };
    } catch (error) {
      this.logger.error(`Fout bij verwerken van bedrijfsnaam ${companyName}: ${error.message}`);
      this.totalErrors++;
      return null;
    }
  }
}

module.exports = PlacesScraper;