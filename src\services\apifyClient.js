/**
 * Apify API Client
 * Service voor het ophalen van places data via Apify Google Maps Scraper
 */

const { ApifyClient } = require('apify-client');

// Environment variabelen
const APIFY_API_TOKEN = process.env.APIFY_API_TOKEN;
const APIFY_ACTOR_ID = process.env.APIFY_ACTOR_ID || '2Mdma1N6Fd0y3QEjR'; // Default Google Maps Scraper

// Logger configuratie (hergeb<PERSON><PERSON> van bestaande logger structuur)
class Logger {
  constructor(options = {}) {
    this.options = {
      logLevel: 'info',
      ...options
    };
  }
  
  info(message, data = null) {
    if (data) {
      console.log(`[INFO] ${message}`, data);
    } else {
      console.log(`[INFO] ${message}`);
    }
  }
  
  warn(message, data = null) {
    if (data) {
      console.warn(`[WARN] ${message}`, data);
    } else {
      console.warn(`[WARN] ${message}`);
    }
  }
  
  error(message, data = null) {
    if (data) {
      console.error(`[ERROR] ${message}`, data);
    } else {
      console.error(`[ERROR] ${message}`);
    }
  }
}

const logger = new Logger();

/**
 * Apify Client voor Google Maps Places scraping
 */
class ApifyPlacesClient {
  constructor(options = {}) {
    this.options = {
      apiToken: APIFY_API_TOKEN,
      actorId: APIFY_ACTOR_ID,
      maxWaitTime: 600000, // 10 minuten max wachttijd
      pollingInterval: 5000, // GEOPTIMALISEERD: 5 seconden polling interval (was 10s)
      ...options
    };

    // Valideer API token bij initialisatie
    if (!this.options.apiToken) {
      logger.error('Apify API token ontbreekt in constructor');
      throw new Error('Apify API token is vereist');
    }

    // Initialiseer Apify client
    this.client = new ApifyClient({
      token: this.options.apiToken
    });

    logger.info('Apify client geïnitialiseerd', {
      actorId: this.options.actorId,
      tokenPrefix: this.options.apiToken.substring(0, 10) + '...'
    });
  }

  /**
   * Zoekt naar plaatsen via Apify Google Maps Scraper
   * @param {Object} searchParams - Zoekparameters
   * @param {Array<string>} searchParams.searchStringsArray - Array van zoektermen
   * @param {string} searchParams.locationQuery - Locatie query (bijv. "Amsterdam, Netherlands")
   * @param {number} searchParams.maxCrawledPlacesPerSearch - Max aantal plaatsen per zoekopdracht
   * @param {string} searchParams.language - Taal code (bijv. "nl", "en")
   * @param {string} searchParams.placeMinimumStars - Minimum sterren filter
   * @param {string} searchParams.website - Website filter ("allPlaces", "onlyPlacesWithWebsite")
   * @param {string} searchParams.searchMatching - Zoek matching ("all", "exact")
   * @param {boolean} searchParams.skipClosedPlaces - Skip gesloten plaatsen
   * @returns {Promise<Array>} Array van plaats objecten
   */
  async searchPlaces(searchParams) {
    try {
      logger.info('Start Apify places search', {
        searchStrings: searchParams.searchStringsArray,
        location: searchParams.locationQuery,
        maxPlaces: searchParams.maxCrawledPlacesPerSearch
      });

      // Prepare Actor input met defaults
      const input = {
        searchStringsArray: searchParams.searchStringsArray || [],
        locationQuery: searchParams.locationQuery || '',
        maxCrawledPlacesPerSearch: searchParams.maxCrawledPlacesPerSearch || 50,
        language: searchParams.language || 'nl',
        placeMinimumStars: searchParams.placeMinimumStars || '',
        website: searchParams.website || 'allPlaces',
        searchMatching: searchParams.searchMatching || 'all',
        skipClosedPlaces: searchParams.skipClosedPlaces || false,
        // Extra opties voor betere data kwaliteit
        includeImages: false, // Sneller zonder images
        includeReviews: false, // Sneller zonder reviews
        maxReviews: 0,
        maxImages: 0
      };

      // Start de Actor run
      logger.info('Starting Apify actor run...');
      const run = await this.client.actor(this.options.actorId).call(input);

      if (!run || !run.defaultDatasetId) {
        throw new Error('Geen dataset ID ontvangen van Apify run');
      }

      logger.info('Apify actor run gestart', {
        runId: run.id,
        datasetId: run.defaultDatasetId,
        status: run.status
      });

      // Haal resultaten op uit de dataset
      logger.info('Ophalen resultaten van Apify dataset...');
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems();

      logger.info('Apify search voltooid', {
        totalResults: items.length,
        runId: run.id
      });

      return items || [];

    } catch (error) {
      logger.error('Fout bij Apify places search', {
        error: error.message,
        searchParams
      });
      throw error;
    }
  }

  /**
   * Zoekt naar plaatsen in een specifieke locatie met meerdere zoektermen
   * @param {Object} params - Parameters
   * @param {string} params.location - Locatie (bijv. "Amsterdam, Netherlands")
   * @param {Array<string>} params.keywords - Array van zoektermen
   * @param {number} params.maxPlacesPerKeyword - Max plaatsen per zoekterm
   * @param {Object} params.options - Extra opties
   * @returns {Promise<Array>} Gecombineerde resultaten van alle zoektermen
   */
  async searchPlacesInLocation(params) {
    const { location, keywords, maxPlacesPerKeyword = 50, options = {} } = params;
    
    try {
      logger.info('Start bulk Apify search', {
        location,
        keywordCount: keywords.length,
        maxPlacesPerKeyword
      });

      const searchParams = {
        searchStringsArray: keywords,
        locationQuery: location,
        maxCrawledPlacesPerSearch: maxPlacesPerKeyword,
        language: options.language || 'nl',
        placeMinimumStars: options.placeMinimumStars || '',
        website: options.website || 'allPlaces',
        searchMatching: options.searchMatching || 'all',
        skipClosedPlaces: options.skipClosedPlaces || false
      };

      const results = await this.searchPlaces(searchParams);
      
      // Dedupliceer resultaten op basis van place_id of naam+adres
      const uniqueResults = this.deduplicateResults(results);
      
      logger.info('Bulk Apify search voltooid', {
        totalResults: results.length,
        uniqueResults: uniqueResults.length,
        duplicatesRemoved: results.length - uniqueResults.length
      });

      return uniqueResults;

    } catch (error) {
      logger.error('Fout bij bulk Apify search', {
        error: error.message,
        location,
        keywords
      });
      throw error;
    }
  }

  /**
   * Dedupliceer resultaten op basis van unieke identifiers
   * @param {Array} results - Array van plaats objecten
   * @returns {Array} Gededupliceerde resultaten
   */
  deduplicateResults(results) {
    const seen = new Set();
    const unique = [];

    for (const result of results) {
      // Gebruik place_id als primaire identifier, anders naam+adres
      const identifier = result.placeId || 
                        result.place_id || 
                        `${result.title || result.name}_${result.address}`;
      
      if (!seen.has(identifier)) {
        seen.add(identifier);
        unique.push(result);
      }
    }

    return unique;
  }

  /**
   * Normaliseert Apify resultaat naar standaard formaat
   * @param {Object} apifyResult - Apify plaats object
   * @returns {Object} Genormaliseerd plaats object
   */
  normalizeApifyResult(apifyResult) {
    return {
      // Basis informatie
      place_id: apifyResult.placeId || apifyResult.place_id,
      name: apifyResult.title || apifyResult.name,
      formatted_address: apifyResult.address,
      
      // Locatie informatie
      geometry: {
        location: {
          lat: apifyResult.location?.lat || apifyResult.latitude,
          lng: apifyResult.location?.lng || apifyResult.longitude
        }
      },
      
      // Contact informatie
      formatted_phone_number: apifyResult.phone,
      website: apifyResult.website,
      
      // Ratings en reviews
      rating: apifyResult.totalScore || apifyResult.rating,
      user_ratings_total: apifyResult.reviewsCount || apifyResult.user_ratings_total,
      
      // Categorieën
      types: apifyResult.categoryName ? [apifyResult.categoryName] : (apifyResult.types || []),
      
      // Extra Apify specifieke data
      apify_data: {
        url: apifyResult.url,
        searchString: apifyResult.searchString,
        rank: apifyResult.rank,
        searchPageUrl: apifyResult.searchPageUrl,
        isAdvertisement: apifyResult.isAdvertisement,
        description: apifyResult.description,
        imageUrl: apifyResult.imageUrl,
        category: apifyResult.categoryName,
        subCategory: apifyResult.subCategoryName,
        priceLevel: apifyResult.priceLevel,
        temporarilyClosed: apifyResult.temporarilyClosed,
        permanentlyClosed: apifyResult.permanentlyClosed
      }
    };
  }
}

module.exports = ApifyPlacesClient;
