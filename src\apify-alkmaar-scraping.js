require('dotenv').config();
const ApifyScraper = require('./apifyScraper');

async function apifyAlkmaarScraping() {
  console.log('🏗️ Alkmaar Bouw & Renovatie Scraping - Apify Modus\n');

  // Alkmaar locatie voor Apify
  const location = 'Alkmaar, Netherlands';
  
  // Alle bouwgerelateerde zoektermen (zelfde als originele alkmaar-scraping.js)
  const keywords = [
    'Dak<PERSON><PERSON>',
    'Timmerman', 
    'Meubelmaker',
    'Houtbewerker',
    'Carport- en pergolabouwer',
    '<PERSON>ukenrenovatie',
    'Badkamerrenovatie',
    '<PERSON><PERSON><PERSON>',
    'Stukadoor',
    'Metselaar',
    '<PERSON><PERSON><PERSON><PERSON>',
    'Bestratingsaannemer',
    'Betonaannemer',
    'Sloopaannemer',
    'Terras- en verandabouwer',
    'Steigerbouwer',
    'Tuinhuisbouwer',
    'Loghuisbouwer',
    'Balustrade- en railingaannemer',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>rbouwer',
    'Plaatwerker',
    'Staalconstructeur',
    '<PERSON><PERSON>',
    'Elektricien',
    'Loodgieter',
    'Gootreiniger',
    'Afvoerspecialist',
    'Verwarmingsmonteur',
    'HVAC-monteur',
    'Parketlegger',
    'Vloerenspecialist',
    'Marmeraannemer',
    'Hovenier',
    'Graafmachinist',
    'Booraannemer',
    'Smid',
    'Glaszetter',
    'Dubbelglaszetter',
    'Ramenwasser',
    'Droogbouwaannemer',
    'Klusjesman',
    'Prefabbouwer',
    'Straatmaker'
  ];

  console.log('📋 Apify Scraping configuratie:');
  console.log(`📍 Locatie: ${location}`);
  console.log(`🔑 Zoektermen: ${keywords.length} termen`);
  console.log(`🚀 Modus: Apify scraping (alleen Apify API)`);
  console.log(`📊 Max plaatsen per zoekterm: 50`);
  console.log(`⏱️ Geschatte duur: 1-2 uur\n`);

  // Toon alle zoektermen
  console.log('🔍 Zoektermen:');
  keywords.forEach((keyword, index) => {
    console.log(`${(index + 1).toString().padStart(2, ' ')}. ${keyword}`);
  });
  console.log('');

  // Configureer scraper voor snelle Apify modus - GEOPTIMALISEERD
  const scraper = new ApifyScraper({
    delayBetweenRequests: 800, // SNELLER: 800ms (was 2000ms) - balans tussen snelheid en rate limits
    maxPlacesPerSearch: 50, // Max plaatsen per zoekterm
    enableFirecrawlEnrichment: false, // Snelle modus
    logLevel: 'info',
    language: 'nl',
    website: 'allPlaces', // Alle plaatsen, ook zonder website
    searchMatching: 'all',
    skipClosedPlaces: false,
    batchSize: 8 // Grotere batches voor productie (meer parallellisme)
  });

  try {
    console.log('🚀 Starten van Alkmaar Apify scraping...\n');
    
    const startTime = Date.now();
    
    await scraper.run({
      location: location,
      keywords: keywords
    });

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000 / 60); // minuten
    
    console.log('\n🎉 Alkmaar Apify scraping voltooid!');
    console.log(`⏱️ Totale duur: ${duration} minuten`);
    console.log('📊 Check je Airtable voor alle verzamelde data');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens Alkmaar Apify scraping:', error.message);
    console.error(error.stack);
  }
}

// Voer scraping uit
if (require.main === module) {
  apifyAlkmaarScraping();
}

module.exports = apifyAlkmaarScraping;
